"""
实时搜索建议服务
基于实时查询 + Redis缓存的搜索建议实现
"""

import logging

from fastapi_cache.decorator import cache
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.article import Article
from app.models.scratch import ScratchProduct
from app.models.video import Video
from app.schemas.search import SearchSuggestionItem

logger = logging.getLogger(__name__)


class RealTimeSearchSuggestionService:
    """实时搜索建议服务"""

    @staticmethod
    def extract_prefixes(text: str, min_length: int = 2, max_length: int = 20) -> list[str]:
        """从文本中提取前缀"""
        if not text or len(text) < min_length:
            return []

        prefixes = []
        # 添加完整文本（如果长度合适）
        if min_length <= len(text) <= max_length:
            prefixes.append(text)

        # 添加前缀
        for i in range(min_length, min(len(text) + 1, max_length + 1)):
            prefix = text[:i]
            if len(prefix.strip()) >= min_length:
                prefixes.append(prefix)

        return list(set(prefixes))  # 去重

    @staticmethod
    @cache(expire=300)  # 缓存5分钟
    async def get_article_suggestions(
        db: AsyncSession, prefix: str, limit: int = 10
    ) -> list[SearchSuggestionItem]:
        """从文章标题获取搜索建议"""

        # 使用PostgreSQL的ILIKE进行前缀匹配
        stmt = (
            select(Article.title)
            .where(
                Article.is_published == True,
                Article.is_approved == True,
                Article.is_deleted == False,
                Article.title.ilike(f"{prefix}%"),
            )
            .distinct()
            .limit(limit * 2)
        )  # 多取一些，后面去重

        result = await db.execute(stmt)
        # 修复：使用fetchall()而不是scalars().all()
        rows = result.fetchall()
        titles = [row[0] for row in rows]

        logger.info(f"查询到 {len(titles)} 个文章标题: {titles}")

        suggestions = []
        seen_keywords = set()

        for title in titles:
            # 提取所有可能的前缀
            prefixes = RealTimeSearchSuggestionService.extract_prefixes(title)

            for keyword in prefixes:
                if (
                    keyword.startswith(prefix)
                    and keyword not in seen_keywords
                    and len(suggestions) < limit
                ):
                    suggestions.append(
                        SearchSuggestionItem(
                            keyword=keyword,
                            type="article",
                            count=len(keyword) * 2,  # 简单的权重计算
                        )
                    )
                    seen_keywords.add(keyword)

        return suggestions

    @staticmethod
    @cache(expire=300)  # 缓存5分钟
    async def get_video_suggestions(
        db: AsyncSession, prefix: str, limit: int = 10
    ) -> list[SearchSuggestionItem]:
        """从视频标题获取搜索建议"""

        stmt = (
            select(Video.title)
            .where(Video.is_published == True, Video.title.ilike(f"{prefix}%"))
            .distinct()
            .limit(limit * 2)
        )

        result = await db.execute(stmt)
        # 修复：使用fetchall()而不是scalars().all()
        rows = result.fetchall()
        titles = [row[0] for row in rows]

        suggestions = []
        seen_keywords = set()

        for title in titles:
            prefixes = RealTimeSearchSuggestionService.extract_prefixes(title)

            for keyword in prefixes:
                if (
                    keyword.startswith(prefix)
                    and keyword not in seen_keywords
                    and len(suggestions) < limit
                ):
                    suggestions.append(
                        SearchSuggestionItem(keyword=keyword, type="video", count=len(keyword) * 2)
                    )
                    seen_keywords.add(keyword)

        return suggestions

    @staticmethod
    @cache(expire=300)  # 缓存5分钟
    async def get_scratch_suggestions(
        db: AsyncSession, prefix: str, limit: int = 10
    ) -> list[SearchSuggestionItem]:
        """从Scratch项目标题获取搜索建议"""

        stmt = (
            select(ScratchProduct.title)
            .where(ScratchProduct.is_published == True, ScratchProduct.title.ilike(f"{prefix}%"))
            .distinct()
            .limit(limit * 2)
        )

        result = await db.execute(stmt)
        # 修复：使用fetchall()而不是scalars().all()
        rows = result.fetchall()
        titles = [row[0] for row in rows]

        suggestions = []
        seen_keywords = set()

        for title in titles:
            prefixes = RealTimeSearchSuggestionService.extract_prefixes(title)

            for keyword in prefixes:
                if (
                    keyword.startswith(prefix)
                    and keyword not in seen_keywords
                    and len(suggestions) < limit
                ):
                    suggestions.append(
                        SearchSuggestionItem(
                            keyword=keyword, type="scratch", count=len(keyword) * 2
                        )
                    )
                    seen_keywords.add(keyword)

        return suggestions

    @staticmethod
    async def get_suggestions(
        db: AsyncSession,
        prefix: str,
        content_type: str = "all",
        limit: int = 10,
    ) -> list[SearchSuggestionItem]:
        """获取搜索建议（实时计算）"""

        if len(prefix.strip()) < 1:
            return []

        all_suggestions = []

        try:
            if content_type in ["all", "article"]:
                article_suggestions = await RealTimeSearchSuggestionService.get_article_suggestions(
                    db, prefix, limit
                )
                all_suggestions.extend(article_suggestions)

            if content_type in ["all", "video"]:
                video_suggestions = await RealTimeSearchSuggestionService.get_video_suggestions(
                    db, prefix, limit
                )
                all_suggestions.extend(video_suggestions)

            if content_type in ["all", "scratch"]:
                scratch_suggestions = await RealTimeSearchSuggestionService.get_scratch_suggestions(
                    db, prefix, limit
                )
                all_suggestions.extend(scratch_suggestions)

            # 去重并按权重排序
            seen_keywords = set()
            unique_suggestions = []

            # 按权重排序
            all_suggestions.sort(key=lambda x: x.count, reverse=True)

            for suggestion in all_suggestions:
                if suggestion.keyword not in seen_keywords and len(unique_suggestions) < limit:
                    unique_suggestions.append(suggestion)
                    seen_keywords.add(suggestion.keyword)

            logger.info(f"实时搜索建议: prefix='{prefix}', 返回{len(unique_suggestions)}个结果")
            return unique_suggestions

        except Exception as e:
            logger.error(f"获取实时搜索建议失败: {e}", exc_info=True)
            return []


# 热门搜索词缓存
class PopularKeywordsService:
    """热门搜索词服务"""

    @staticmethod
    @cache(expire=3600)  # 缓存1小时
    async def get_popular_keywords(
        db: AsyncSession, content_type: str = "all", limit: int = 20
    ) -> list[SearchSuggestionItem]:
        """获取热门搜索词"""

        # 这里可以基于搜索历史、访问量等计算热门词
        # 暂时返回一些预设的热门词
        popular_keywords = [
            "React",
            "Vue",
            "Python",
            "JavaScript",
            "AI",
            "机器学习",
            "Docker",
            "Kubernetes",
            "微服务",
            "前端",
            "后端",
            "数据库",
            "算法",
            "设计模式",
            "项目管理",
            "产品设计",
        ]

        suggestions = []
        for i, keyword in enumerate(popular_keywords[:limit]):
            suggestions.append(
                SearchSuggestionItem(
                    keyword=keyword,
                    type="trending",
                    count=100 - i,  # 递减权重
                )
            )

        return suggestions

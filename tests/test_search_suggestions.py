"""
搜索建议功能测试
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pytest
from sqlalchemy import select

from app.db.session import SessionLocal
from app.models.article import Article
from app.services.search_suggestion_service import RealTimeSearchSuggestionService


class TestSearchSuggestions:
    """搜索建议测试类"""
    
    @pytest.mark.asyncio
    async def test_database_query(self):
        """测试数据库查询"""
        async with SessionLocal() as db:
            # 直接查询数据库
            stmt = select(Article.title).where(
                Article.is_published == True,
                Article.is_approved == True,
                Article.is_deleted == False,
                Article.title.ilike('关于%')
            ).limit(5)
            
            result = await db.execute(stmt)
            # 修复：使用fetchall()而不是scalars().all()
            rows = result.fetchall()
            titles = [row[0] for row in rows]
            print(f'数据库查询结果: 找到 {len(titles)} 个标题:')
            for title in titles:
                print(f'  - "{title}" (长度: {len(title)})')
            
            assert len(titles) > 0, "应该找到至少一个匹配的标题"
    
    def test_extract_prefixes(self):
        """测试前缀提取功能"""
        title = '关于行业未来的思考'
        prefixes = RealTimeSearchSuggestionService.extract_prefixes(title)
        print(f'标题: {title}')
        print(f'提取的前缀 ({len(prefixes)} 个):')
        for prefix in prefixes:
            print(f'  - "{prefix}"')
        
        # 验证前缀提取
        assert '关于' in prefixes
        assert '关于行业' in prefixes
        assert '关于行业未来的思考' in prefixes
        
        # 测试匹配
        test_prefix = '关于'
        matching_prefixes = [p for p in prefixes if p.startswith(test_prefix)]
        print(f'\n匹配 "{test_prefix}" 的前缀 ({len(matching_prefixes)} 个):')
        for prefix in matching_prefixes:
            print(f'  - "{prefix}"')
        
        assert len(matching_prefixes) > 0, "应该有匹配的前缀"
    
    @pytest.mark.asyncio
    async def test_article_suggestions(self):
        """测试文章建议功能"""
        async with SessionLocal() as db:
            print('开始测试文章建议...')
            
            # 添加调试信息
            stmt = select(Article.title).where(
                Article.is_published == True,
                Article.is_approved == True,
                Article.is_deleted == False,
                Article.title.ilike('关于%')
            ).limit(5)
            
            result = await db.execute(stmt)
            # 修复：使用fetchall()而不是scalars().all()
            rows = result.fetchall()
            titles = [row[0] for row in rows]
            print(f'DEBUG: 查询到 {len(titles)} 个文章标题: {titles}')
            
            # 测试建议服务
            suggestions = await RealTimeSearchSuggestionService.get_article_suggestions(
                db=db,
                prefix='关于',
                limit=10
            )
            print(f'文章建议: 找到 {len(suggestions)} 个建议:')
            for s in suggestions:
                print(f'  - "{s.keyword}" (类型: {s.type}, 权重: {s.count})')
            
            # 如果没有建议，手动测试逻辑
            if len(suggestions) == 0 and len(titles) > 0:
                print("\n手动测试前缀提取逻辑:")
                for title in titles:
                    print(f"处理标题: {title}")
                    prefixes = RealTimeSearchSuggestionService.extract_prefixes(title)
                    print(f"提取到前缀: {prefixes}")
                    
                    matching = [p for p in prefixes if p.startswith('关于')]
                    print(f"匹配'关于'的前缀: {matching}")
    
    @pytest.mark.asyncio
    async def test_full_suggestions_service(self):
        """测试完整的建议服务"""
        async with SessionLocal() as db:
            print('测试完整建议服务...')
            
            suggestions = await RealTimeSearchSuggestionService.get_suggestions(
                db=db,
                prefix='关于',
                content_type='all',
                limit=10
            )
            print(f'完整建议服务: 找到 {len(suggestions)} 个建议:')
            for s in suggestions:
                print(f'  - "{s.keyword}" (类型: {s.type}, 权重: {s.count})')
    
    @pytest.mark.asyncio
    async def test_api_endpoint(self):
        """测试API端点"""
        import requests
        import json
        
        print('测试搜索建议API端点...')
        url = 'http://localhost:8000/api/v1/search/suggestions'
        params = {'q': '关于', 'limit': 10}
        
        try:
            response = requests.get(url, params=params, timeout=10)
            print('Status Code:', response.status_code)
            
            if response.status_code == 200:
                data = response.json()
                if 'data' in data:
                    suggestions = data['data']['suggestions']
                    print(f'API返回 {len(suggestions)} 个建议:')
                    for suggestion in suggestions:
                        print(f'  - "{suggestion["keyword"]}" (类型: {suggestion["type"]}, 权重: {suggestion["count"]})')
                else:
                    print('响应格式:', json.dumps(data, ensure_ascii=False, indent=2))
            else:
                print('Error:', response.text)
        except requests.exceptions.RequestException as e:
            print(f'请求失败: {e}')


async def main():
    """主测试函数"""
    test_instance = TestSearchSuggestions()
    
    print("=== 1. 测试数据库查询 ===")
    await test_instance.test_database_query()
    
    print("\n=== 2. 测试前缀提取 ===")
    test_instance.test_extract_prefixes()
    
    print("\n=== 3. 测试文章建议 ===")
    await test_instance.test_article_suggestions()
    
    print("\n=== 4. 测试完整建议服务 ===")
    await test_instance.test_full_suggestions_service()
    
    print("\n=== 5. 测试API端点 ===")
    await test_instance.test_api_endpoint()


if __name__ == "__main__":
    asyncio.run(main())

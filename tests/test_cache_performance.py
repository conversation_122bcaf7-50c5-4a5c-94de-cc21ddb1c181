"""
测试搜索建议的缓存性能
"""

import asyncio
import time
import requests
import json


async def test_cache_performance():
    """测试缓存性能"""
    
    url = 'http://localhost:8000/api/v1/search/suggestions'
    params = {'q': '关于', 'limit': 10}
    
    print("=== 测试搜索建议缓存性能 ===")
    
    # 第一次请求（缓存未命中）
    print("\n1. 第一次请求（缓存未命中）:")
    start_time = time.time()
    response1 = requests.get(url, params=params)
    end_time = time.time()
    
    print(f"状态码: {response1.status_code}")
    print(f"响应时间: {(end_time - start_time) * 1000:.2f}ms")
    
    if response1.status_code == 200:
        data = response1.json()
        suggestions = data['data']['suggestions']
        print(f"找到 {len(suggestions)} 个建议")
    
    # 第二次请求（缓存命中）
    print("\n2. 第二次请求（缓存命中）:")
    start_time = time.time()
    response2 = requests.get(url, params=params)
    end_time = time.time()
    
    print(f"状态码: {response2.status_code}")
    print(f"响应时间: {(end_time - start_time) * 1000:.2f}ms")
    
    if response2.status_code == 200:
        data = response2.json()
        suggestions = data['data']['suggestions']
        print(f"找到 {len(suggestions)} 个建议")
    
    # 第三次请求（缓存命中）
    print("\n3. 第三次请求（缓存命中）:")
    start_time = time.time()
    response3 = requests.get(url, params=params)
    end_time = time.time()
    
    print(f"状态码: {response3.status_code}")
    print(f"响应时间: {(end_time - start_time) * 1000:.2f}ms")
    
    # 测试不同的查询
    print("\n4. 测试不同查询（缓存未命中）:")
    params2 = {'q': '关于行业', 'limit': 5}
    start_time = time.time()
    response4 = requests.get(url, params=params2)
    end_time = time.time()
    
    print(f"状态码: {response4.status_code}")
    print(f"响应时间: {(end_time - start_time) * 1000:.2f}ms")
    
    if response4.status_code == 200:
        data = response4.json()
        suggestions = data['data']['suggestions']
        print(f"找到 {len(suggestions)} 个建议:")
        for suggestion in suggestions:
            print(f'  - "{suggestion["keyword"]}" (权重: {suggestion["count"]})')


if __name__ == "__main__":
    asyncio.run(test_cache_performance())
